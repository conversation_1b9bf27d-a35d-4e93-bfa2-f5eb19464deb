{
  service: 'quipgen-elearning-backend',
  environment: 'development',
  level: 'error',
  message: 'Database connection failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string',
  stack: 'Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n' +
    '    at D:\\Task_Project\\backend\\node_modules\\pg-pool\\index.js:45:11\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async connectDatabase (D:\\Task_Project\\backend\\src\\config\\database.js:44:20)\n' +
    '    at async seedData (D:\\Task_Project\\backend\\src\\database\\seed.js:6:5)\n' +
    '    at async main (D:\\Task_Project\\backend\\src\\database\\seed.js:204:7)',
  timestamp: '2025-08-04 06:16:53'
}
