import '../models/course.dart';
import '../models/user_progress.dart';
import '../models/category.dart';
import '../models/lesson.dart';
import '../models/enrollment.dart';
import 'http_client.dart';
import 'api_config.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final HttpClient _httpClient = HttpClient();

  // Authentication endpoints
  Future<Map<String, dynamic>> getUserProfile() async {
    final response = await _httpClient.get(ApiConfig.profileUrl);
    return response['data'];
  }

  Future<Map<String, dynamic>> updateUserProfile({
    String? name,
    String? phone,
  }) async {
    final body = <String, dynamic>{};
    if (name != null) body['name'] = name;
    if (phone != null) body['phone'] = phone;

    final response = await _httpClient.put(ApiConfig.profileUrl, body: body);
    return response['data'];
  }

  Future<void> deleteUserAccount() async {
    await _httpClient.delete('${ApiConfig.authUrl}/account');
  }

  Future<Map<String, dynamic>> verifyToken() async {
    final response = await _httpClient.get(ApiConfig.verifyTokenUrl);
    return response['data'];
  }

  // Course endpoints
  Future<List<Course>> getCourses({
    int page = 1,
    int limit = 10,
    String? category,
    String? level,
    String? search,
    bool? featured,
    bool? popular,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (category != null) queryParams['category'] = category;
    if (level != null) queryParams['level'] = level;
    if (search != null) queryParams['search'] = search;
    if (featured != null) queryParams['featured'] = featured.toString();
    if (popular != null) queryParams['popular'] = popular.toString();

    final response = await _httpClient.get(ApiConfig.coursesUrl, queryParams: queryParams);
    final coursesData = response['data']['courses'] as List;
    return coursesData.map((json) => Course.fromJson(json)).toList();
  }

  Future<Course> getCourseById(int courseId) async {
    final response = await _httpClient.get(ApiConfig.courseDetailsUrl(courseId));
    return Course.fromJson(response['data']);
  }

  Future<List<Course>> getFeaturedCourses({int limit = 6}) async {
    final queryParams = {'limit': limit.toString()};
    final response = await _httpClient.get(ApiConfig.featuredCoursesUrl, queryParams: queryParams);
    final coursesData = response['data'] as List;
    return coursesData.map((json) => Course.fromJson(json)).toList();
  }

  Future<List<Course>> getPopularCourses({int limit = 6}) async {
    final queryParams = {'limit': limit.toString()};
    final response = await _httpClient.get(ApiConfig.popularCoursesUrl, queryParams: queryParams);
    final coursesData = response['data'] as List;
    return coursesData.map((json) => Course.fromJson(json)).toList();
  }

  Future<List<Category>> getCategories() async {
    final response = await _httpClient.get(ApiConfig.categoriesUrl);
    final categoriesData = response['data'] as List;
    return categoriesData.map((json) => Category.fromJson(json)).toList();
  }

  // User enrollment and progress endpoints
  Future<List<Enrollment>> getUserEnrollments({int page = 1, int limit = 10}) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    final response = await _httpClient.get(ApiConfig.enrollmentsUrl, queryParams: queryParams);
    final enrollmentsData = response['data']['enrollments'] as List;
    return enrollmentsData.map((json) => Enrollment.fromJson(json)).toList();
  }

  Future<Map<String, dynamic>> getUserProgress(int courseId) async {
    final response = await _httpClient.get(ApiConfig.userProgressUrl(courseId));
    return response['data'];
  }

  Future<Map<String, dynamic>> updateLessonProgress(
    int courseId,
    int lessonId, {
    required double progressPercentage,
    int? timeSpentMinutes,
    bool? isCompleted,
  }) async {
    final body = {
      'progressPercentage': progressPercentage,
      if (timeSpentMinutes != null) 'timeSpentMinutes': timeSpentMinutes,
      if (isCompleted != null) 'isCompleted': isCompleted,
    };

    final response = await _httpClient.put(
      ApiConfig.updateProgressUrl(courseId, lessonId),
      body: body,
    );
    return response['data'];
  }

  // Lesson endpoints
  Future<List<Lesson>> getCourseLessons(int courseId) async {
    final response = await _httpClient.get(ApiConfig.courseLessonsUrl(courseId));
    final lessonsData = response['data']['lessons'] as List;
    return lessonsData.map((json) => Lesson.fromJson(json)).toList();
  }

  Future<Lesson> getLessonById(int lessonId) async {
    final response = await _httpClient.get(ApiConfig.lessonDetailsUrl(lessonId));
    return Lesson.fromJson(response['data']);
  }

  Future<void> markLessonAsAccessed(int lessonId) async {
    await _httpClient.post(ApiConfig.lessonAccessUrl(lessonId));
  }

  Future<Map<String, dynamic>?> getNextLesson(int lessonId) async {
    final response = await _httpClient.get(ApiConfig.nextLessonUrl(lessonId));
    return response['data'];
  }

  // Payment endpoints
  Future<Map<String, dynamic>> createPaymentOrder(int courseId, {String currency = 'INR'}) async {
    final body = {
      'courseId': courseId,
      'currency': currency,
    };

    final response = await _httpClient.post(ApiConfig.createOrderUrl, body: body);
    return response['data'];
  }

  Future<Map<String, dynamic>> verifyPayment({
    required String razorpayOrderId,
    required String razorpayPaymentId,
    required String razorpaySignature,
    required int courseId,
  }) async {
    final body = {
      'razorpay_order_id': razorpayOrderId,
      'razorpay_payment_id': razorpayPaymentId,
      'razorpay_signature': razorpaySignature,
      'course_id': courseId,
    };

    final response = await _httpClient.post(ApiConfig.verifyPaymentUrl, body: body);
    return response['data'];
  }

  Future<Map<String, dynamic>> getPaymentStatus(String orderId) async {
    final response = await _httpClient.get(ApiConfig.paymentStatusUrl(orderId));
    return response['data'];
  }

  Future<List<Map<String, dynamic>>> getPaymentHistory({int page = 1, int limit = 10}) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    final response = await _httpClient.get(ApiConfig.paymentHistoryUrl, queryParams: queryParams);
    return List<Map<String, dynamic>>.from(response['data']['payments']);
  }

  // Search functionality
  Future<List<Course>> searchCourses(String query, {int page = 1, int limit = 10}) async {
    return getCourses(search: query, page: page, limit: limit);
  }

  // Helper method to check if user is authenticated
  bool get isAuthenticated => _httpClient.isAuthenticated;

  // Dispose resources
  void dispose() {
    _httpClient.dispose();
  }
}
