# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/quipgen_elearning
DB_HOST=localhost
DB_PORT=5432
DB_NAME=quipgen_elearning
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Firebase Configuration
FIREBASE_ADMIN_SDK_PATH=./config/firebase-admin-sdk.json
FIREBASE_PROJECT_ID=your_firebase_project_id

# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Server Configuration
PORT=3000
NODE_ENV=development

# Security
JWT_SECRET=your_jwt_secret_key_here
CORS_ORIGIN=http://localhost:3000

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_DIR=./logs
