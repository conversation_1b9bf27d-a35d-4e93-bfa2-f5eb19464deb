class Course {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final String instructor;
  final double price;
  final double rating;
  final int totalLessons;
  final int duration; // in minutes
  final String category;
  final String level; // Beginner, Intermediate, Advanced
  final List<String> tags;
  final bool isFeatured;
  final bool isPopular;
  final DateTime createdAt;
  final DateTime updatedAt;

  Course({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.instructor,
    required this.price,
    required this.rating,
    required this.totalLessons,
    required this.duration,
    required this.category,
    required this.level,
    required this.tags,
    this.isFeatured = false,
    this.isPopular = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      instructor: json['instructor'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      rating: (json['rating'] ?? 0).toDouble(),
      totalLessons: json['totalLessons'] ?? 0,
      duration: json['duration'] ?? 0,
      category: json['category'] ?? '',
      level: json['level'] ?? 'Beginner',
      tags: List<String>.from(json['tags'] ?? []),
      isFeatured: json['isFeatured'] ?? false,
      isPopular: json['isPopular'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'instructor': instructor,
      'price': price,
      'rating': rating,
      'totalLessons': totalLessons,
      'duration': duration,
      'category': category,
      'level': level,
      'tags': tags,
      'isFeatured': isFeatured,
      'isPopular': isPopular,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedDuration {
    final hours = duration ~/ 60;
    final minutes = duration % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }
}
