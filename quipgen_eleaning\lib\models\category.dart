class Category {
  final String id;
  final String name;
  final String description;
  final String iconName;
  final String color;
  final int courseCount;
  final bool isPopular;
  final DateTime createdAt;

  Category({
    required this.id,
    required this.name,
    required this.description,
    required this.iconName,
    required this.color,
    required this.courseCount,
    this.isPopular = false,
    required this.createdAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      iconName: json['iconName'] ?? 'category',
      color: json['color'] ?? '#6137C4',
      courseCount: json['courseCount'] ?? 0,
      isPopular: json['isPopular'] ?? false,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconName': iconName,
      'color': color,
      'courseCount': courseCount,
      'isPopular': isPopular,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class LearningStreak {
  final String userId;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastActivityDate;
  final List<DateTime> activityDates;

  LearningStreak({
    required this.userId,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastActivityDate,
    required this.activityDates,
  });

  factory LearningStreak.fromJson(Map<String, dynamic> json) {
    return LearningStreak(
      userId: json['userId'] ?? '',
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      lastActivityDate: DateTime.parse(json['lastActivityDate'] ?? DateTime.now().toIso8601String()),
      activityDates: (json['activityDates'] as List<dynamic>?)
          ?.map((date) => DateTime.parse(date))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastActivityDate': lastActivityDate.toIso8601String(),
      'activityDates': activityDates.map((date) => date.toIso8601String()).toList(),
    };
  }

  bool get isActiveToday {
    final today = DateTime.now();
    final lastActivity = lastActivityDate;
    return today.year == lastActivity.year &&
           today.month == lastActivity.month &&
           today.day == lastActivity.day;
  }
}
