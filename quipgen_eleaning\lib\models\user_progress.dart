class UserProgress {
  final String id;
  final String userId;
  final String courseId;
  final int completedLessons;
  final int totalLessons;
  final double progressPercentage;
  final DateTime lastAccessedAt;
  final DateTime startedAt;
  final DateTime? completedAt;
  final int timeSpentMinutes;
  final bool isBookmarked;
  final bool isDownloaded;

  UserProgress({
    required this.id,
    required this.userId,
    required this.courseId,
    required this.completedLessons,
    required this.totalLessons,
    required this.progressPercentage,
    required this.lastAccessedAt,
    required this.startedAt,
    this.completedAt,
    required this.timeSpentMinutes,
    this.isBookmarked = false,
    this.isDownloaded = false,
  });

  factory UserProgress.fromJson(Map<String, dynamic> json) {
    return UserProgress(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      courseId: json['courseId'] ?? '',
      completedLessons: json['completedLessons'] ?? 0,
      totalLessons: json['totalLessons'] ?? 0,
      progressPercentage: (json['progressPercentage'] ?? 0).toDouble(),
      lastAccessedAt: DateTime.parse(json['lastAccessedAt'] ?? DateTime.now().toIso8601String()),
      startedAt: DateTime.parse(json['startedAt'] ?? DateTime.now().toIso8601String()),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      timeSpentMinutes: json['timeSpentMinutes'] ?? 0,
      isBookmarked: json['isBookmarked'] ?? false,
      isDownloaded: json['isDownloaded'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'courseId': courseId,
      'completedLessons': completedLessons,
      'totalLessons': totalLessons,
      'progressPercentage': progressPercentage,
      'lastAccessedAt': lastAccessedAt.toIso8601String(),
      'startedAt': startedAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'timeSpentMinutes': timeSpentMinutes,
      'isBookmarked': isBookmarked,
      'isDownloaded': isDownloaded,
    };
  }

  bool get isCompleted => progressPercentage >= 100.0;
  bool get isInProgress => progressPercentage > 0 && progressPercentage < 100.0;
  String get formattedTimeSpent {
    final hours = timeSpentMinutes ~/ 60;
    final minutes = timeSpentMinutes % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }
}
