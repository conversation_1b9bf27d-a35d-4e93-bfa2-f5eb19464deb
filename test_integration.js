const http = require("http");

// Simple test script to verify backend endpoints
const testEndpoints = [
  {
    name: "Health Check",
    path: "/health",
    method: "GET",
  },
  {
    name: "Get Categories",
    path: "/api/courses/categories",
    method: "GET",
  },
  {
    name: "Get Courses",
    path: "/api/courses",
    method: "GET",
  },
  {
    name: "Get Featured Courses",
    path: "/api/courses/featured",
    method: "GET",
  },
  {
    name: "Get Popular Courses",
    path: "/api/courses/popular",
    method: "GET",
  },
];

const makeRequest = (path, method = "GET") => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: "localhost",
      port: 3001,
      path: path,
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
    };

    const req = http.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData,
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            data: data,
          });
        }
      });
    });

    req.on("error", (error) => {
      reject(error);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error("Request timeout"));
    });

    req.end();
  });
};

const runTests = async () => {
  console.log("🚀 Starting API Integration Tests...\n");

  let passedTests = 0;
  let totalTests = testEndpoints.length;

  for (const test of testEndpoints) {
    try {
      console.log(`Testing: ${test.name}`);
      console.log(`  ${test.method} ${test.path}`);

      const result = await makeRequest(test.path, test.method);

      if (result.statusCode === 200) {
        console.log(`  ✅ PASS - Status: ${result.statusCode}`);

        // Show some data for verification
        if (result.data && typeof result.data === "object") {
          if (result.data.success) {
            console.log(`  📊 Success: ${result.data.success}`);
            if (result.data.data) {
              if (Array.isArray(result.data.data)) {
                console.log(
                  `  📦 Data: Array with ${result.data.data.length} items`
                );
              } else {
                console.log(
                  `  📦 Data: Object with keys: ${Object.keys(
                    result.data.data
                  ).join(", ")}`
                );
              }
            }
          }
        }
        passedTests++;
      } else {
        console.log(`  ❌ FAIL - Status: ${result.statusCode}`);
        if (result.data && result.data.message) {
          console.log(`  💬 Message: ${result.data.message}`);
        }
      }
    } catch (error) {
      console.log(`  ❌ ERROR - ${error.message}`);
    }

    console.log(""); // Empty line for readability
  }

  console.log("📊 Test Results:");
  console.log(`  Passed: ${passedTests}/${totalTests}`);
  console.log(
    `  Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`
  );

  if (passedTests === totalTests) {
    console.log(
      "\n🎉 All tests passed! Backend is ready for Flutter integration."
    );
  } else {
    console.log(
      "\n⚠️  Some tests failed. Please check the backend server and database."
    );
  }
};

// Check if server is running first
const checkServer = async () => {
  try {
    await makeRequest("/health");
    console.log("✅ Server is running on http://localhost:3001\n");
    return true;
  } catch (error) {
    console.log("❌ Server is not running on http://localhost:3001");
    console.log("Please start the backend server first:");
    console.log("  cd backend");
    console.log("  npm run dev");
    return false;
  }
};

const main = async () => {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await runTests();
  }
};

main().catch(console.error);
