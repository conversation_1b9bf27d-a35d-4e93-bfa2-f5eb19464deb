enum AchievementType {
  coursesCompleted,
  streakDays,
  timeSpent,
  skillMastery,
  firstCourse,
  perfectScore,
}

class Achievement {
  final String id;
  final String title;
  final String description;
  final String iconUrl;
  final AchievementType type;
  final int targetValue;
  final String badgeColor;
  final DateTime createdAt;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconUrl,
    required this.type,
    required this.targetValue,
    required this.badgeColor,
    required this.createdAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      iconUrl: json['iconUrl'] ?? '',
      type: AchievementType.values.firstWhere(
        (e) => e.toString() == 'AchievementType.${json['type']}',
        orElse: () => AchievementType.coursesCompleted,
      ),
      targetValue: json['targetValue'] ?? 0,
      badgeColor: json['badgeColor'] ?? '#FFD700',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'iconUrl': iconUrl,
      'type': type.toString().split('.').last,
      'targetValue': targetValue,
      'badgeColor': badgeColor,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class UserAchievement {
  final String id;
  final String userId;
  final String achievementId;
  final int currentValue;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final DateTime createdAt;

  UserAchievement({
    required this.id,
    required this.userId,
    required this.achievementId,
    required this.currentValue,
    required this.isUnlocked,
    this.unlockedAt,
    required this.createdAt,
  });

  factory UserAchievement.fromJson(Map<String, dynamic> json) {
    return UserAchievement(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      achievementId: json['achievementId'] ?? '',
      currentValue: json['currentValue'] ?? 0,
      isUnlocked: json['isUnlocked'] ?? false,
      unlockedAt: json['unlockedAt'] != null ? DateTime.parse(json['unlockedAt']) : null,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'achievementId': achievementId,
      'currentValue': currentValue,
      'isUnlocked': isUnlocked,
      'unlockedAt': unlockedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  double getProgress(int targetValue) {
    if (targetValue == 0) return 0.0;
    return (currentValue / targetValue).clamp(0.0, 1.0);
  }
}
