import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_config.dart';

class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  ApiException(this.message, {this.statusCode, this.data});

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

class HttpClient {
  static final HttpClient _instance = HttpClient._internal();
  factory HttpClient() => _instance;
  HttpClient._internal();

  final http.Client _client = http.Client();

  // Get authentication token from Firebase
  Future<String?> _getAuthToken() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        return await user.getIdToken();
      }
      return null;
    } catch (e) {
      print('Error getting auth token: $e');
      return null;
    }
  }

  // Build headers with authentication
  Future<Map<String, String>> _buildHeaders({Map<String, String>? additionalHeaders}) async {
    final headers = Map<String, String>.from(ApiConfig.defaultHeaders);
    
    // Add authentication token if available
    final token = await _getAuthToken();
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    // Add any additional headers
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }
    
    return headers;
  }

  // Handle HTTP response
  dynamic _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    
    try {
      final data = json.decode(response.body);
      
      if (statusCode >= 200 && statusCode < 300) {
        return data;
      } else {
        final message = data['message'] ?? 'Unknown error occurred';
        throw ApiException(message, statusCode: statusCode, data: data);
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      
      // Handle non-JSON responses
      if (statusCode >= 400) {
        throw ApiException(
          'HTTP $statusCode: ${response.reasonPhrase}',
          statusCode: statusCode,
        );
      }
      
      throw ApiException('Failed to parse response: $e');
    }
  }

  // GET request
  Future<dynamic> get(String url, {Map<String, String>? queryParams}) async {
    try {
      final uri = Uri.parse(url);
      final finalUri = queryParams != null 
          ? uri.replace(queryParameters: queryParams)
          : uri;
      
      final headers = await _buildHeaders();
      
      final response = await _client
          .get(finalUri, headers: headers)
          .timeout(ApiConfig.receiveTimeout);
      
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on HttpException {
      throw ApiException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  // POST request
  Future<dynamic> post(String url, {dynamic body, Map<String, String>? additionalHeaders}) async {
    try {
      final headers = await _buildHeaders(additionalHeaders: additionalHeaders);
      
      final response = await _client
          .post(
            Uri.parse(url),
            headers: headers,
            body: body != null ? json.encode(body) : null,
          )
          .timeout(ApiConfig.receiveTimeout);
      
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on HttpException {
      throw ApiException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  // PUT request
  Future<dynamic> put(String url, {dynamic body, Map<String, String>? additionalHeaders}) async {
    try {
      final headers = await _buildHeaders(additionalHeaders: additionalHeaders);
      
      final response = await _client
          .put(
            Uri.parse(url),
            headers: headers,
            body: body != null ? json.encode(body) : null,
          )
          .timeout(ApiConfig.receiveTimeout);
      
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on HttpException {
      throw ApiException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  // DELETE request
  Future<dynamic> delete(String url, {Map<String, String>? additionalHeaders}) async {
    try {
      final headers = await _buildHeaders(additionalHeaders: additionalHeaders);
      
      final response = await _client
          .delete(Uri.parse(url), headers: headers)
          .timeout(ApiConfig.receiveTimeout);
      
      return _handleResponse(response);
    } on SocketException {
      throw ApiException('No internet connection');
    } on HttpException {
      throw ApiException('Network error occurred');
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Request failed: $e');
    }
  }

  // Check if user is authenticated
  bool get isAuthenticated => FirebaseAuth.instance.currentUser != null;

  // Dispose resources
  void dispose() {
    _client.close();
  }
}
