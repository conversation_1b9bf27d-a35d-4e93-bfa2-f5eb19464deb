import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  // controller to handle page navigation
  final PageController _pageController = PageController();

  // Tracks the current page index
  int _currentIndex = 0;

  // List of onboarding steps : title, description, image
  final List<Map<String, String>> _pages = [
    {
      "title": "better way to learning is calling you!",
      "description":
          "Discover a smarter, more engaging approach to learning that inspires you to grow every day. Start your journey to knowledge now!",
      "image": "assets/onboarding/step1.png",
    },
    {
      "title": "Find yourself by doing whatever you do!",
      "description":
          "Unlock your true potential by exploring new skills and experiences. Every step you take brings you closer to your goals.",
      "image": "assets/onboarding/step2.png",
    },
    {
      "title": "It's not just learning, it's a promise!",
      "description":
          "We’re committed to supporting your growth and success. Join us and turn your learning into a lifelong promise to yourself.",
      "image": "assets/onboarding/step3.png",
    },
  ];

  // Navigate to the next page or got to the home screen if on last page
  void _nextPage() {
    if (_currentIndex < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      Navigator.pushReplacementNamed(context, '/home');
    }
  }

  void _skipOnboarding() {
    Navigator.pushReplacementNamed(context, '/home');
  }

  // Build a horizontal rectangular stepper to indicate progress
  // A rounded, circular stepper with a fluid fill animation for the active step.
  Widget _buildStepper() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_pages.length, (index) {
        final bool isActive = _currentIndex == index;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          width: isActive ? 80 : 16,
          height: 4,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: isActive
                ? const Color.fromARGB(255, 166, 33, 243)
                : const Color.fromARGB(255, 221, 221, 221),
            borderRadius: BorderRadius.circular(2),
          ),
        );
      }),
    );
  }

  // Main UI Build Method
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // step indicator
            Padding(padding: const EdgeInsets.all(16), child: _buildStepper()),
            // Onboarding Content area using PageView
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _pages.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemBuilder: (context, index) {
                  final item = _pages[index];
                  return Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AnimatedBuilder(
                          animation: _pageController,
                          builder: (context, child) {
                            double page = _currentIndex.toDouble();
                            try {
                              if (_pageController.hasClients &&
                                  _pageController.page != null) {
                                page = _pageController.page!;
                              }
                            } catch (_) {}
                            double delta = (index - page);

                            // for the secode last page use a smooth bounce-in effect
                            if (index == _pages.length - 2) {
                              double t = (1.0 - delta.abs().clamp(0.0, 1.0));

                              double bounce =
                                  -200 * (1 - t) + 20 * (1 - t) * (1 - t);

                              return AnimatedOpacity(
                                opacity: delta.abs() < 1.0 ? 1.0 : 0.0,
                                duration: const Duration(milliseconds: 400),
                                child: Transform.translate(
                                  offset: Offset(0, bounce),
                                  child: Image.asset(
                                    item['image']!,
                                    width: 200,
                                    height: 200,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              );
                            } else {
                              // All other pages simple fall from top to bottom
                              double t = (1.0 - delta.abs().clamp(0.0, 1.0));
                              double y = -200 * (1 - t);

                              return AnimatedOpacity(
                                opacity: delta.abs() < 1.0 ? 1.0 : 0.0,
                                duration: const Duration(milliseconds: 400),
                                child: Transform.translate(
                                  offset: Offset(0, y),
                                  child: Image.asset(
                                    item['image']!,
                                    width: 200,
                                    height: 200,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                        const SizedBox(height: 20),
                        Text(
                          item['title']!.substring(0, 1).toUpperCase() +
                              item['title']!.substring(1),
                          style: GoogleFonts.lato(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          softWrap: true,
                          textScaler: TextScaler.linear(1.2),
                          textWidthBasis: TextWidthBasis.parent,
                        ),
                        const SizedBox(height: 10),
                        Text(
                          item['description']!,
                          style: GoogleFonts.lato(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Bottom Navigation
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: _currentIndex == _pages.length - 1
                    ? [
                        SizedBox(
                          height: 48,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 60,
                              ),
                              backgroundColor: const Color.fromARGB(
                                255,
                                17,
                                17,
                                17,
                              ),
                            ),
                            onPressed: _nextPage,
                            child: Text(
                              "Get Started",
                              style: GoogleFonts.lato(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ]
                    : [
                        SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: TextButton(
                            style: TextButton.styleFrom(
                              backgroundColor: Colors.grey.shade200,
                            ),
                            onPressed: () {
                              if (_pageController.hasClients) {
                                _pageController.animateToPage(
                                  _pages.length - 1,
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                              _skipOnboarding();
                            },
                            child: Text(
                              "Skip",
                              style: GoogleFonts.lato(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 18),
                        SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color.fromARGB(
                                255,
                                17,
                                17,
                                17,
                              ),
                              // keep color always the same
                              foregroundColor: Colors.white,
                              disabledBackgroundColor: const Color.fromARGB(
                                255,
                                17,
                                17,
                                17,
                              ),
                              disabledForegroundColor: Colors.white,
                            ),
                            onPressed: () {
                              if (_pageController.hasClients) {
                                _pageController.animateToPage(
                                  (_currentIndex + 1).clamp(
                                    0,
                                    _pages.length - 1,
                                  ),
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                            child: Text(
                              "Next",
                              style: GoogleFonts.lato(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
