sky_engine
3.7
file:///C:/flutter/bin/cache/pkg/sky_engine/
file:///C:/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///C:/flutter/packages/flutter/
file:///C:/flutter/packages/flutter/lib/
flutter_test
3.7
file:///C:/flutter/packages/flutter_test/
file:///C:/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///C:/flutter/packages/flutter_web_plugins/
file:///C:/flutter/packages/flutter_web_plugins/lib/
_flutterfire_internals
3.2
file:///D:/PubCache/hosted/pub.dev/_flutterfire_internals-1.3.35/
file:///D:/PubCache/hosted/pub.dev/_flutterfire_internals-1.3.35/lib/
async
3.4
file:///D:/PubCache/hosted/pub.dev/async-2.13.0/
file:///D:/PubCache/hosted/pub.dev/async-2.13.0/lib/
boolean_selector
3.1
file:///D:/PubCache/hosted/pub.dev/boolean_selector-2.1.2/
file:///D:/PubCache/hosted/pub.dev/boolean_selector-2.1.2/lib/
characters
3.4
file:///D:/PubCache/hosted/pub.dev/characters-1.4.0/
file:///D:/PubCache/hosted/pub.dev/characters-1.4.0/lib/
clock
3.4
file:///D:/PubCache/hosted/pub.dev/clock-1.1.2/
file:///D:/PubCache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///D:/PubCache/hosted/pub.dev/collection-1.19.1/
file:///D:/PubCache/hosted/pub.dev/collection-1.19.1/lib/
crypto
3.4
file:///D:/PubCache/hosted/pub.dev/crypto-3.0.6/
file:///D:/PubCache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///D:/PubCache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///D:/PubCache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
fake_async
3.3
file:///D:/PubCache/hosted/pub.dev/fake_async-1.3.3/
file:///D:/PubCache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///D:/PubCache/hosted/pub.dev/ffi-2.1.4/
file:///D:/PubCache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///D:/PubCache/hosted/pub.dev/file-7.0.1/
file:///D:/PubCache/hosted/pub.dev/file-7.0.1/lib/
firebase_auth
3.2
file:///D:/PubCache/hosted/pub.dev/firebase_auth-4.20.0/
file:///D:/PubCache/hosted/pub.dev/firebase_auth-4.20.0/lib/
firebase_auth_platform_interface
3.2
file:///D:/PubCache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/
file:///D:/PubCache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/lib/
firebase_auth_web
3.2
file:///D:/PubCache/hosted/pub.dev/firebase_auth_web-5.12.0/
file:///D:/PubCache/hosted/pub.dev/firebase_auth_web-5.12.0/lib/
firebase_core
2.18
file:///D:/PubCache/hosted/pub.dev/firebase_core-2.32.0/
file:///D:/PubCache/hosted/pub.dev/firebase_core-2.32.0/lib/
firebase_core_platform_interface
3.2
file:///D:/PubCache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/
file:///D:/PubCache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/lib/
firebase_core_web
3.2
file:///D:/PubCache/hosted/pub.dev/firebase_core_web-2.17.5/
file:///D:/PubCache/hosted/pub.dev/firebase_core_web-2.17.5/lib/
flutter_lints
3.5
file:///D:/PubCache/hosted/pub.dev/flutter_lints-5.0.0/
file:///D:/PubCache/hosted/pub.dev/flutter_lints-5.0.0/lib/
font_awesome_flutter
3.0
file:///D:/PubCache/hosted/pub.dev/font_awesome_flutter-10.8.0/
file:///D:/PubCache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/
google_fonts
2.14
file:///D:/PubCache/hosted/pub.dev/google_fonts-6.3.0/
file:///D:/PubCache/hosted/pub.dev/google_fonts-6.3.0/lib/
google_identity_services_web
3.4
file:///D:/PubCache/hosted/pub.dev/google_identity_services_web-0.3.3+1/
file:///D:/PubCache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib/
google_sign_in
3.6
file:///D:/PubCache/hosted/pub.dev/google_sign_in-6.3.0/
file:///D:/PubCache/hosted/pub.dev/google_sign_in-6.3.0/lib/
google_sign_in_android
3.6
file:///D:/PubCache/hosted/pub.dev/google_sign_in_android-6.2.1/
file:///D:/PubCache/hosted/pub.dev/google_sign_in_android-6.2.1/lib/
google_sign_in_ios
3.4
file:///D:/PubCache/hosted/pub.dev/google_sign_in_ios-5.9.0/
file:///D:/PubCache/hosted/pub.dev/google_sign_in_ios-5.9.0/lib/
google_sign_in_platform_interface
3.4
file:///D:/PubCache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/
file:///D:/PubCache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/
google_sign_in_web
3.4
file:///D:/PubCache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
file:///D:/PubCache/hosted/pub.dev/google_sign_in_web-0.12.4+4/lib/
http
3.4
file:///D:/PubCache/hosted/pub.dev/http-1.4.0/
file:///D:/PubCache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///D:/PubCache/hosted/pub.dev/http_parser-4.1.2/
file:///D:/PubCache/hosted/pub.dev/http_parser-4.1.2/lib/
leak_tracker
3.2
file:///D:/PubCache/hosted/pub.dev/leak_tracker-10.0.9/
file:///D:/PubCache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///D:/PubCache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///D:/PubCache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///D:/PubCache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///D:/PubCache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///D:/PubCache/hosted/pub.dev/lints-5.1.1/
file:///D:/PubCache/hosted/pub.dev/lints-5.1.1/lib/
matcher
3.4
file:///D:/PubCache/hosted/pub.dev/matcher-0.12.17/
file:///D:/PubCache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///D:/PubCache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///D:/PubCache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///D:/PubCache/hosted/pub.dev/meta-1.16.0/
file:///D:/PubCache/hosted/pub.dev/meta-1.16.0/lib/
path
3.4
file:///D:/PubCache/hosted/pub.dev/path-1.9.1/
file:///D:/PubCache/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.4
file:///D:/PubCache/hosted/pub.dev/path_provider-2.1.5/
file:///D:/PubCache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///D:/PubCache/hosted/pub.dev/path_provider_android-2.2.17/
file:///D:/PubCache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///D:/PubCache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///D:/PubCache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///D:/PubCache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///D:/PubCache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///D:/PubCache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///D:/PubCache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///D:/PubCache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///D:/PubCache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
platform
3.2
file:///D:/PubCache/hosted/pub.dev/platform-3.1.6/
file:///D:/PubCache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///D:/PubCache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///D:/PubCache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
shared_preferences
3.5
file:///D:/PubCache/hosted/pub.dev/shared_preferences-2.5.3/
file:///D:/PubCache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///D:/PubCache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///D:/PubCache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///D:/PubCache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///D:/PubCache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///D:/PubCache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///D:/PubCache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///D:/PubCache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///D:/PubCache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///D:/PubCache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///D:/PubCache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///D:/PubCache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///D:/PubCache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
source_span
3.1
file:///D:/PubCache/hosted/pub.dev/source_span-1.10.1/
file:///D:/PubCache/hosted/pub.dev/source_span-1.10.1/lib/
stack_trace
3.4
file:///D:/PubCache/hosted/pub.dev/stack_trace-1.12.1/
file:///D:/PubCache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///D:/PubCache/hosted/pub.dev/stream_channel-2.1.4/
file:///D:/PubCache/hosted/pub.dev/stream_channel-2.1.4/lib/
string_scanner
3.1
file:///D:/PubCache/hosted/pub.dev/string_scanner-1.4.1/
file:///D:/PubCache/hosted/pub.dev/string_scanner-1.4.1/lib/
term_glyph
3.1
file:///D:/PubCache/hosted/pub.dev/term_glyph-1.2.2/
file:///D:/PubCache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///D:/PubCache/hosted/pub.dev/test_api-0.7.4/
file:///D:/PubCache/hosted/pub.dev/test_api-0.7.4/lib/
typed_data
3.5
file:///D:/PubCache/hosted/pub.dev/typed_data-1.4.0/
file:///D:/PubCache/hosted/pub.dev/typed_data-1.4.0/lib/
vector_math
2.14
file:///D:/PubCache/hosted/pub.dev/vector_math-2.1.4/
file:///D:/PubCache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///D:/PubCache/hosted/pub.dev/vm_service-15.0.0/
file:///D:/PubCache/hosted/pub.dev/vm_service-15.0.0/lib/
web
3.3
file:///D:/PubCache/hosted/pub.dev/web-0.5.1/
file:///D:/PubCache/hosted/pub.dev/web-0.5.1/lib/
xdg_directories
3.3
file:///D:/PubCache/hosted/pub.dev/xdg_directories-1.1.0/
file:///D:/PubCache/hosted/pub.dev/xdg_directories-1.1.0/lib/
quipgen_eleaning
3.8
file:///D:/Task_Project/quipgen_eleaning/
file:///D:/Task_Project/quipgen_eleaning/lib/
2
