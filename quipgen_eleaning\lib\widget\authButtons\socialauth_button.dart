import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SocialAuthButton extends StatefulWidget {
  final String text; // Button label text
  final Future<void> Function() onPressed; // Async login/signup logic
  final String iconPath; // Path to image icon
  final bool showLoading; // Optional: show loading spinner

  const SocialAuthButton({
    super.key,
    required this.text,
    required this.onPressed,
    required this.iconPath,
    this.showLoading = true,
  });

  @override
  State<SocialAuthButton> createState() => _SocialAuthButtonState();
}

class _SocialAuthButtonState extends State<SocialAuthButton> {
  bool _isLoading = false;

  // Handles button press and manages loading state
  Future<void> _handlePress() async {
    if (_isLoading) return;

    setState(() => _isLoading = true);
    try {
      await widget.onPressed();
    } catch (e) {
      // Optional: show error or log
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get theme colors for dynamic UI
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final buttonBackgroundColor = isDarkMode
        ? Colors.grey[700]
        : Colors.grey[200];
    final textColor = theme.textTheme.bodyMedium?.color;
    final progressIndicatorColor = textColor;

    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _handlePress, // Disable while loading
      icon: _isLoading && widget.showLoading
          ? SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: progressIndicatorColor,
              ),
            )
          : Image.asset(widget.iconPath, width: 24, height: 24),
      label: Text(
        widget.text,
        style: GoogleFonts.roboto(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: textColor,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: buttonBackgroundColor,
        foregroundColor: textColor,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 0,
      ),
    );
  }
}
