class Lesson {
  final String id;
  final String courseId;
  final String title;
  final String description;
  final int duration; // in seconds
  final String videoUrl;
  final String thumbnailUrl;
  final int order;
  final bool isCompleted;
  final bool isLocked;
  final String type; // video, quiz, reading, assignment
  final List<String> resources; // URLs to additional resources
  final DateTime createdAt;

  Lesson({
    required this.id,
    required this.courseId,
    required this.title,
    required this.description,
    required this.duration,
    required this.videoUrl,
    required this.thumbnailUrl,
    required this.order,
    this.isCompleted = false,
    this.isLocked = false,
    this.type = 'video',
    this.resources = const [],
    required this.createdAt,
  });

  factory Lesson.fromJson(Map<String, dynamic> json) {
    return Lesson(
      id: json['id'] ?? '',
      courseId: json['courseId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      duration: json['duration'] ?? 0,
      videoUrl: json['videoUrl'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      order: json['order'] ?? 0,
      isCompleted: json['isCompleted'] ?? false,
      isLocked: json['isLocked'] ?? false,
      type: json['type'] ?? 'video',
      resources: List<String>.from(json['resources'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'courseId': courseId,
      'title': title,
      'description': description,
      'duration': duration,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'order': order,
      'isCompleted': isCompleted,
      'isLocked': isLocked,
      'type': type,
      'resources': resources,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get typeIcon {
    switch (type) {
      case 'video':
        return '🎥';
      case 'quiz':
        return '❓';
      case 'reading':
        return '📖';
      case 'assignment':
        return '📝';
      default:
        return '📄';
    }
  }
}

class CourseReview {
  final String id;
  final String courseId;
  final String userId;
  final String userName;
  final String userAvatar;
  final double rating;
  final String comment;
  final DateTime createdAt;
  final List<String> helpfulUserIds;

  CourseReview({
    required this.id,
    required this.courseId,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.rating,
    required this.comment,
    required this.createdAt,
    this.helpfulUserIds = const [],
  });

  factory CourseReview.fromJson(Map<String, dynamic> json) {
    return CourseReview(
      id: json['id'] ?? '',
      courseId: json['courseId'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userAvatar: json['userAvatar'] ?? '',
      rating: (json['rating'] ?? 0).toDouble(),
      comment: json['comment'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      helpfulUserIds: List<String>.from(json['helpfulUserIds'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'courseId': courseId,
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'rating': rating,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
      'helpfulUserIds': helpfulUserIds,
    };
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }
}

class CourseStats {
  final String courseId;
  final int totalEnrollments;
  final double averageRating;
  final int totalReviews;
  final int completionRate; // percentage
  final Map<int, int> ratingDistribution; // rating -> count

  CourseStats({
    required this.courseId,
    required this.totalEnrollments,
    required this.averageRating,
    required this.totalReviews,
    required this.completionRate,
    required this.ratingDistribution,
  });

  factory CourseStats.fromJson(Map<String, dynamic> json) {
    return CourseStats(
      courseId: json['courseId'] ?? '',
      totalEnrollments: json['totalEnrollments'] ?? 0,
      averageRating: (json['averageRating'] ?? 0).toDouble(),
      totalReviews: json['totalReviews'] ?? 0,
      completionRate: json['completionRate'] ?? 0,
      ratingDistribution: Map<int, int>.from(json['ratingDistribution'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'courseId': courseId,
      'totalEnrollments': totalEnrollments,
      'averageRating': averageRating,
      'totalReviews': totalReviews,
      'completionRate': completionRate,
      'ratingDistribution': ratingDistribution,
    };
  }

  String get formattedEnrollments {
    if (totalEnrollments >= 1000) {
      return '${(totalEnrollments / 1000).toStringAsFixed(1)}k';
    }
    return totalEnrollments.toString();
  }
}
