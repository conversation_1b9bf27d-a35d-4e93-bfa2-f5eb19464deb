# QuipGen E-Learning Platform

A comprehensive Flutter-based e-learning application with Node.js backend for course management and secure payments.

## Project Structure

### Frontend (Flutter)
- Authentication (Firebase Auth)
- Course browsing and search
- User dashboard and progress tracking
- Payment integration with Razorpay

### Backend Requirements (Node.js + PostgreSQL)

## Backend API Specification

### Overview
Implement a Node.js backend with PostgreSQL to handle course purchases and access control. Only authenticated users can purchase courses, and course access is granted only after successful payment verification through Razorpay.

### Tech Stack Requirements
- **Runtime**: Node.js (v18+)
- **Framework**: Express.js
- **Database**: PostgreSQL
- **Payment Gateway**: Razorpay
- **Authentication**: Firebase Admin SDK (server-side verification)
- **ORM/Query Builder**: Prisma or Sequelize (recommended)

### Database Schema

```sql
-- Users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    firebase_uid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    profile_image_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon_name VARCHAR(100),
    color VARCHAR(7), -- Hex color code
    is_popular BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Courses table
CREATE TABLE courses (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    image_url TEXT,
    instructor VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    rating DECIMAL(3,2) DEFAULT 0.0,
    total_lessons INTEGER DEFAULT 0,
    duration_minutes INTEGER DEFAULT 0,
    category_id INTEGER REFERENCES categories(id),
    level VARCHAR(50) DEFAULT 'Beginner', -- Beginner, Intermediate, Advanced
    tags TEXT[], -- Array of tags
    is_featured BOOLEAN DEFAULT FALSE,
    is_popular BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Lessons table
CREATE TABLE lessons (
    id SERIAL PRIMARY KEY,
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT,
    duration_minutes INTEGER DEFAULT 0,
    order_index INTEGER NOT NULL,
    is_preview BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enrollments table
CREATE TABLE enrollments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    razorpay_order_id VARCHAR(255) UNIQUE,
    razorpay_payment_id VARCHAR(255),
    payment_status VARCHAR(50) DEFAULT 'pending', -- pending, completed, failed, refunded
    amount_paid DECIMAL(10,2),
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_completed_at TIMESTAMP,
    UNIQUE(user_id, course_id)
);

-- User Progress table
CREATE TABLE user_progress (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    lesson_id INTEGER REFERENCES lessons(id) ON DELETE CASCADE,
    is_completed BOOLEAN DEFAULT FALSE,
    progress_percentage DECIMAL(5,2) DEFAULT 0.0,
    time_spent_minutes INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    UNIQUE(user_id, course_id, lesson_id)
);

-- Reviews table
CREATE TABLE reviews (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, course_id)
);
```

### Required API Endpoints

#### Authentication Middleware
- Verify Firebase JWT tokens using Firebase Admin SDK
- Extract user information from token
- Create/update user in PostgreSQL database

#### Course Management
```
GET /api/courses
- Get all courses with pagination and filters
- Query params: page, limit, category, level, search, featured, popular

GET /api/courses/:id
- Get detailed course information
- Include lessons (only previews for non-enrolled users)

GET /api/categories
- Get all course categories
```

#### User Management
```
GET /api/user/profile
- Get authenticated user profile
- Requires: Authentication

PUT /api/user/profile
- Update user profile
- Requires: Authentication

GET /api/user/enrollments
- Get user's enrolled courses with progress
- Requires: Authentication

GET /api/user/progress/:courseId
- Get detailed progress for a specific course
- Requires: Authentication + Course enrollment
```

#### Payment & Enrollment
```
POST /api/courses/:id/purchase
- Create Razorpay order for course purchase
- Requires: Authentication
- Body: { currency: 'INR' }
- Response: { orderId, amount, currency, key }

POST /api/payment/verify
- Verify Razorpay payment and complete enrollment
- Requires: Authentication
- Body: {
    razorpay_order_id,
    razorpay_payment_id,
    razorpay_signature,
    course_id
  }
- Response: { success, enrollment_id }

GET /api/courses/:id/access
- Check if user has access to course content
- Requires: Authentication
- Response: { hasAccess, enrollment_date, progress }
```

#### Course Content Access
```
GET /api/courses/:courseId/lessons
- Get course lessons (only for enrolled users)
- Requires: Authentication + Valid enrollment

GET /api/lessons/:id
- Get lesson details and video URL
- Requires: Authentication + Course enrollment

POST /api/lessons/:id/progress
- Update lesson progress
- Requires: Authentication + Course enrollment
- Body: { progress_percentage, time_spent_minutes }
```

### Razorpay Integration Steps

1. **Setup Razorpay Account**
   - Sign up at [Razorpay Dashboard](https://dashboard.razorpay.com/)
   - Get API Key ID and Secret
   - Configure webhooks for payment updates

2. **Install Dependencies**
   ```bash
   npm install razorpay crypto
   ```

3. **Payment Flow Implementation**
   - Create order using Razorpay API
   - Return order details to Flutter app
   - Verify payment signature on backend
   - Update enrollment status in database

4. **Security Measures**
   - Validate payment signatures using HMAC SHA256
   - Store API keys in environment variables
   - Implement payment verification middleware

### Environment Variables
```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/quipgen_elearning

# Firebase
FIREBASE_ADMIN_SDK_PATH=path/to/firebase-admin-sdk.json

# Razorpay
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Server
PORT=3000
NODE_ENV=development

# Security
JWT_SECRET=your_jwt_secret
CORS_ORIGIN=http://localhost:3000
```

### Implementation Guidelines

1. **Authentication Flow**
   - Flutter app sends Firebase ID token in Authorization header
   - Backend verifies token using Firebase Admin SDK
   - Extract user info and sync with PostgreSQL
   - Attach user data to request object

2. **Payment Security**
   - Always verify payments on server-side
   - Use webhook signatures for additional security
   - Log all payment attempts for auditing
   - Implement idempotency for payment endpoints

3. **Course Access Control**
   - Check enrollment status before serving content
   - Verify payment completion before granting access
   - Implement rate limiting for content access

4. **Error Handling**
   - Return consistent error response format
   - Log errors with appropriate context
   - Handle Razorpay API failures gracefully

5. **Performance Optimization**
   - Implement database indexing on frequently queried fields
   - Use connection pooling for PostgreSQL
   - Cache course data using Redis (optional)

### Testing Requirements

- Unit tests for payment verification logic
- Integration tests for course enrollment flow
- Mock Razorpay API for testing environments
- Test authentication middleware with Firebase

### Deployment Considerations

- Use HTTPS in production
- Set up proper CORS policies
- Configure Razorpay webhooks for production URLs
- Implement health check endpoints
- Set up logging and monitoring

## Flutter Frontend Resources

A few resources to get you started with Flutter development:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)
- [Online documentation](https://docs.flutter.dev/)

## Getting Started with Development

1. Set up Flutter development environment
2. Configure Firebase project and download config files
3. Set up Node.js backend with PostgreSQL
4. Configure Razorpay account and get API keys
5. Run database migrations
6. Start both frontend and backend servers
