import '../models/course.dart';
import '../models/user_progress.dart';
import '../models/achievement.dart';
import '../models/category.dart';
import '../models/lesson.dart';
import '../models/enrollment.dart';
import 'api_service.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  final ApiService _apiService = ApiService();

  // Mock data for demonstration
  List<Course> get mockCourses => [
    Course(
      id: '1',
      title: 'Complete UX/UI & App Design',
      description:
          'Master the fundamentals of user experience and interface design',
      imageUrl:
          'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      instructor: '<PERSON>',
      price: 26.99,
      rating: 4.9,
      totalLessons: 24,
      duration: 480,
      category: 'Design',
      level: 'Intermediate',
      tags: ['UI/UX', 'Design', 'Mobile'],
      isFeatured: true,
      isPopular: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    Course(
      id: '2',
      title: 'Digital Marketing Masterclass',
      description: 'Learn digital marketing strategies that actually work',
      imageUrl:
          'https://images.unsplash.com/photo-1557862921-37829c790f19?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1171&q=80',
      instructor: 'Mike Chen',
      price: 19.99,
      rating: 4.7,
      totalLessons: 18,
      duration: 360,
      category: 'Marketing',
      level: 'Beginner',
      tags: ['Marketing', 'Social Media', 'SEO'],
      isPopular: true,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    Course(
      id: '3',
      title: 'Flutter App Development',
      description: 'Build beautiful cross-platform mobile apps with Flutter',
      imageUrl:
          'https://images.unsplash.com/photo-1617040619263-41c5a9ca7521?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      instructor: 'Alex Rodriguez',
      price: 29.99,
      rating: 4.8,
      totalLessons: 30,
      duration: 600,
      category: 'Development',
      level: 'Intermediate',
      tags: ['Flutter', 'Mobile', 'Dart'],
      isFeatured: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    Course(
      id: '4',
      title: 'Python Programming',
      description: 'Learn Python from scratch to advanced level',
      imageUrl:
          'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg?auto=compress&fit=crop&w=800&q=80',
      instructor: 'Dr. Emily Watson',
      price: 21.99,
      rating: 4.6,
      totalLessons: 15,
      duration: 300,
      category: 'Development',
      level: 'Beginner',
      tags: ['Python', 'Programming', 'Backend'],
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(days: 7)),
    ),
  ];

  List<UserProgress> get mockUserProgress => [
    UserProgress(
      id: '1',
      userId: 'user1',
      courseId: '1',
      completedLessons: 18,
      totalLessons: 24,
      progressPercentage: 75.0,
      lastAccessedAt: DateTime.now().subtract(const Duration(hours: 2)),
      startedAt: DateTime.now().subtract(const Duration(days: 10)),
      timeSpentMinutes: 240,
      isBookmarked: true,
    ),
    UserProgress(
      id: '2',
      userId: 'user1',
      courseId: '2',
      completedLessons: 5,
      totalLessons: 18,
      progressPercentage: 28.0,
      lastAccessedAt: DateTime.now().subtract(const Duration(days: 1)),
      startedAt: DateTime.now().subtract(const Duration(days: 5)),
      timeSpentMinutes: 90,
    ),
    UserProgress(
      id: '3',
      userId: 'user1',
      courseId: '3',
      completedLessons: 30,
      totalLessons: 30,
      progressPercentage: 100.0,
      lastAccessedAt: DateTime.now().subtract(const Duration(days: 3)),
      startedAt: DateTime.now().subtract(const Duration(days: 20)),
      completedAt: DateTime.now().subtract(const Duration(days: 3)),
      timeSpentMinutes: 600,
      isBookmarked: true,
    ),
  ];

  List<Category> get mockCategories => [
    Category(
      id: '1',
      name: 'Design',
      description: 'UI/UX, Graphic Design, and Visual Arts',
      iconName: 'design_services',
      color: '#FF6B6B',
      courseCount: 45,
      isPopular: true,
      createdAt: DateTime.now().subtract(const Duration(days: 100)),
    ),
    Category(
      id: '2',
      name: 'Development',
      description: 'Programming, Web & Mobile Development',
      iconName: 'code',
      color: '#4ECDC4',
      courseCount: 78,
      isPopular: true,
      createdAt: DateTime.now().subtract(const Duration(days: 95)),
    ),
    Category(
      id: '3',
      name: 'Marketing',
      description: 'Digital Marketing, SEO, Social Media',
      iconName: 'campaign',
      color: '#45B7D1',
      courseCount: 32,
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
    ),
    Category(
      id: '4',
      name: 'Business',
      description: 'Entrepreneurship, Management, Finance',
      iconName: 'business',
      color: '#96CEB4',
      courseCount: 28,
      createdAt: DateTime.now().subtract(const Duration(days: 85)),
    ),
  ];

  LearningStreak get mockLearningStreak => LearningStreak(
    userId: 'user1',
    currentStreak: 7,
    longestStreak: 15,
    lastActivityDate: DateTime.now(),
    activityDates: List.generate(
      7,
      (index) => DateTime.now().subtract(Duration(days: index)),
    ),
  );

  List<Achievement> get mockAchievements => [
    Achievement(
      id: '1',
      title: 'First Steps',
      description: 'Complete your first course',
      iconUrl: '🎯',
      type: AchievementType.firstCourse,
      targetValue: 1,
      badgeColor: '#FFD700',
      createdAt: DateTime.now().subtract(const Duration(days: 50)),
    ),
    Achievement(
      id: '2',
      title: 'Streak Master',
      description: 'Maintain a 7-day learning streak',
      iconUrl: '🔥',
      type: AchievementType.streakDays,
      targetValue: 7,
      badgeColor: '#FF6B6B',
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
    ),
    Achievement(
      id: '3',
      title: 'Course Collector',
      description: 'Complete 5 courses',
      iconUrl: '📚',
      type: AchievementType.coursesCompleted,
      targetValue: 5,
      badgeColor: '#4ECDC4',
      createdAt: DateTime.now().subtract(const Duration(days: 40)),
    ),
  ];

  List<UserAchievement> get mockUserAchievements => [
    UserAchievement(
      id: '1',
      userId: 'user1',
      achievementId: '1',
      currentValue: 1,
      isUnlocked: true,
      unlockedAt: DateTime.now().subtract(const Duration(days: 20)),
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
    UserAchievement(
      id: '2',
      userId: 'user1',
      achievementId: '2',
      currentValue: 7,
      isUnlocked: true,
      unlockedAt: DateTime.now(),
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
    ),
    UserAchievement(
      id: '3',
      userId: 'user1',
      achievementId: '3',
      currentValue: 1,
      isUnlocked: false,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
  ];

  // Methods to get data - now using real API calls
  Future<List<Course>> getCourses() async {
    try {
      return await _apiService.getCourses();
    } catch (e) {
      print('Error fetching courses: $e');
      // Fallback to mock data if API fails
      return mockCourses;
    }
  }

  Future<List<Course>> getFeaturedCourses() async {
    try {
      return await _apiService.getFeaturedCourses();
    } catch (e) {
      print('Error fetching featured courses: $e');
      // Fallback to mock data if API fails
      return mockCourses.where((course) => course.isFeatured).toList();
    }
  }

  Future<List<Course>> getPopularCourses() async {
    try {
      return await _apiService.getPopularCourses();
    } catch (e) {
      print('Error fetching popular courses: $e');
      // Fallback to mock data if API fails
      return mockCourses.where((course) => course.isPopular).toList();
    }
  }

  Future<List<UserProgress>> getUserProgress(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return mockUserProgress
        .where((progress) => progress.userId == userId)
        .toList();
  }

  Future<List<UserProgress>> getContinueLearning(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return mockUserProgress
        .where((progress) => progress.userId == userId && progress.isInProgress)
        .toList()
      ..sort((a, b) => b.lastAccessedAt.compareTo(a.lastAccessedAt));
  }

  Future<List<Category>> getCategories() async {
    try {
      return await _apiService.getCategories();
    } catch (e) {
      print('Error fetching categories: $e');
      // Fallback to mock data if API fails
      return mockCategories;
    }
  }

  Future<LearningStreak> getLearningStreak(String userId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return mockLearningStreak;
  }

  Future<List<UserAchievement>> getUserAchievements(String userId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return mockUserAchievements.where((ua) => ua.userId == userId).toList();
  }

  Future<List<Course>> searchCourses(String query) async {
    try {
      return await _apiService.searchCourses(query);
    } catch (e) {
      print('Error searching courses: $e');
      // Fallback to mock data search if API fails
      if (query.isEmpty) return mockCourses;

      return mockCourses
          .where(
            (course) =>
                course.title.toLowerCase().contains(query.toLowerCase()) ||
                course.description.toLowerCase().contains(
                  query.toLowerCase(),
                ) ||
                course.tags.any(
                  (tag) => tag.toLowerCase().contains(query.toLowerCase()),
                ),
          )
          .toList();
    }
  }

  // Mock data for lessons
  List<Lesson> getMockLessons(String courseId) {
    return [
      Lesson(
        id: '1',
        courseId: courseId,
        title: 'Introduction to the Course',
        description: 'Welcome to the course! Learn what you\'ll achieve.',
        duration: 180, // 3 minutes
        videoUrl: 'https://example.com/video1.mp4',
        thumbnailUrl: 'https://via.placeholder.com/300x200',
        order: 1,
        isCompleted: true,
        type: 'video',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Lesson(
        id: '2',
        courseId: courseId,
        title: 'Setting Up Your Environment',
        description: 'Install and configure all necessary tools.',
        duration: 420, // 7 minutes
        videoUrl: 'https://example.com/video2.mp4',
        thumbnailUrl: 'https://via.placeholder.com/300x200',
        order: 2,
        isCompleted: true,
        type: 'video',
        createdAt: DateTime.now().subtract(const Duration(days: 29)),
      ),
      Lesson(
        id: '3',
        courseId: courseId,
        title: 'Basic Concepts and Principles',
        description: 'Understanding the fundamental concepts.',
        duration: 600, // 10 minutes
        videoUrl: 'https://example.com/video3.mp4',
        thumbnailUrl: 'https://via.placeholder.com/300x200',
        order: 3,
        isCompleted: false,
        type: 'video',
        createdAt: DateTime.now().subtract(const Duration(days: 28)),
      ),
      Lesson(
        id: '4',
        courseId: courseId,
        title: 'Quiz: Test Your Knowledge',
        description: 'Quick quiz to test what you\'ve learned so far.',
        duration: 300, // 5 minutes
        videoUrl: '',
        thumbnailUrl: 'https://via.placeholder.com/300x200',
        order: 4,
        isCompleted: false,
        isLocked: true,
        type: 'quiz',
        createdAt: DateTime.now().subtract(const Duration(days: 27)),
      ),
    ];
  }

  // Mock data for course reviews
  List<CourseReview> getMockReviews(String courseId) {
    return [
      CourseReview(
        id: '1',
        courseId: courseId,
        userId: 'user1',
        userName: 'Sarah Johnson',
        userAvatar:
            'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        rating: 5.0,
        comment:
            'Excellent course! Very well structured and easy to follow. The instructor explains everything clearly.',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        helpfulUserIds: ['user2', 'user3'],
      ),
      CourseReview(
        id: '2',
        courseId: courseId,
        userId: 'user2',
        userName: 'Mike Chen',
        userAvatar:
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        rating: 4.0,
        comment:
            'Great content, but could use more practical examples. Overall very satisfied with the learning experience.',
        createdAt: DateTime.now().subtract(const Duration(days: 12)),
        helpfulUserIds: ['user1'],
      ),
      CourseReview(
        id: '3',
        courseId: courseId,
        userId: 'user3',
        userName: 'Emily Watson',
        userAvatar:
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        rating: 5.0,
        comment:
            'Perfect for beginners! I learned so much and feel confident to apply these skills in real projects.',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        helpfulUserIds: ['user1', 'user2', 'user4'],
      ),
    ];
  }

  // Mock course stats
  CourseStats getMockCourseStats(String courseId) {
    return CourseStats(
      courseId: courseId,
      totalEnrollments: 2847,
      averageRating: 4.8,
      totalReviews: 342,
      completionRate: 87,
      ratingDistribution: {5: 245, 4: 67, 3: 20, 2: 7, 1: 3},
    );
  }

  // Mock coupons
  List<Coupon> get mockCoupons => [
    Coupon(
      id: '1',
      code: 'WELCOME20',
      description: '20% off for new students',
      discountPercentage: 20,
      minimumAmount: 10,
      validFrom: DateTime.now().subtract(const Duration(days: 30)),
      validUntil: DateTime.now().add(const Duration(days: 30)),
      usageLimit: 1000,
      usedCount: 245,
    ),
    Coupon(
      id: '2',
      code: 'SAVE10',
      description: 'Save \$10 on any course',
      discountAmount: 10,
      minimumAmount: 25,
      validFrom: DateTime.now().subtract(const Duration(days: 15)),
      validUntil: DateTime.now().add(const Duration(days: 15)),
      usageLimit: 500,
      usedCount: 123,
    ),
  ];

  // API methods for new functionality
  Future<List<Lesson>> getCourseLessons(String courseId) async {
    try {
      final id = int.tryParse(courseId);
      if (id == null) throw Exception('Invalid course ID');
      return await _apiService.getCourseLessons(id);
    } catch (e) {
      print('Error fetching course lessons: $e');
      // Fallback to mock data if API fails
      return getMockLessons(courseId);
    }
  }

  Future<List<CourseReview>> getCourseReviews(String courseId) async {
    await Future.delayed(const Duration(milliseconds: 400));
    return getMockReviews(courseId);
  }

  Future<CourseStats> getCourseStats(String courseId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return getMockCourseStats(courseId);
  }

  Future<Course?> getCourseById(String courseId) async {
    try {
      final id = int.tryParse(courseId);
      if (id == null) throw Exception('Invalid course ID');
      return await _apiService.getCourseById(id);
    } catch (e) {
      print('Error fetching course details: $e');
      // Fallback to mock data if API fails
      return mockCourses.firstWhere(
        (course) => course.id == courseId,
        orElse: () => mockCourses.first, // Return first course as fallback
      );
    }
  }

  Future<List<Course>> getRelatedCourses(
    String courseId,
    String category,
  ) async {
    try {
      // For now, use search by category as a workaround
      final courses = await _apiService.getCourses(category: category);
      return courses.where((course) => course.id != courseId).take(3).toList();
    } catch (e) {
      print('Error fetching related courses: $e');
      // Fallback to mock data if API fails
      return mockCourses
          .where(
            (course) => course.category == category && course.id != courseId,
          )
          .take(3)
          .toList();
    }
  }

  Future<Coupon?> validateCoupon(String code, String courseId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final coupon = mockCoupons.firstWhere(
      (c) => c.code.toLowerCase() == code.toLowerCase(),
      orElse: () => mockCoupons.first,
    );

    if (coupon.isValid && coupon.isApplicableForCourse(courseId)) {
      return coupon;
    }
    return null;
  }

  Future<bool> enrollInCourse(
    String userId,
    String courseId,
    PaymentInfo paymentInfo,
  ) async {
    await Future.delayed(const Duration(milliseconds: 1000));
    // Simulate enrollment process
    return true;
  }
}
