# Flutter-Backend Integration Setup Guide

This guide will help you set up and test the complete integration between the Flutter frontend and Node.js backend.

## Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database
- Flutter SDK
- Firebase project setup

## Backend Setup

### 1. Install Dependencies

```bash
cd backend
npm install
```

### 2. Environment Configuration

Create a `.env` file in the backend directory:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/quipgen_elearning
DB_HOST=localhost
DB_PORT=5432
DB_NAME=quipgen_elearning
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Firebase Configuration
FIREBASE_ADMIN_SDK_PATH=./config/firebase-admin-sdk.json
FIREBASE_PROJECT_ID=your_firebase_project_id

# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# Server Configuration
PORT=3000
NODE_ENV=development

# Security
JWT_SECRET=your_jwt_secret_key_here
CORS_ORIGIN=http://localhost:3000

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_DIR=./logs
```

### 3. Database Setup

```bash
# Run migrations
npm run migrate

# Seed sample data
npm run seed
```

### 4. Start Backend Server

```bash
# Development mode
npm run dev

# Production mode
npm start
```

The server will start on http://localhost:3000

## Flutter Setup

### 1. Install Dependencies

```bash
cd quipgen_eleaning
flutter pub get
```

### 2. Firebase Configuration

Make sure your `firebase_options.dart` file is properly configured with your Firebase project settings.

### 3. API Configuration

The API base URL is configured in `lib/services/api_config.dart`. For development, it's set to `http://localhost:3000/api`.

For Android emulator, you might need to use `http://********:3000/api` instead.

### 4. Run Flutter App

```bash
flutter run
```

## Testing the Integration

### 1. Test Backend Endpoints

Run the integration test script from the project root:

```bash
node test_integration.js
```

This will test all the main API endpoints and verify they're working correctly.

### 2. Test Flutter App

1. Start the backend server
2. Run the Flutter app
3. Test the following features:
   - Browse courses (should load from API)
   - View course details
   - Search courses
   - Browse categories
   - User authentication (Firebase)

## API Endpoints

### Authentication
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `DELETE /api/auth/account` - Delete user account
- `GET /api/auth/verify` - Verify authentication token

### Courses
- `GET /api/courses` - Get all courses (with pagination and filters)
- `GET /api/courses/:id` - Get course details
- `GET /api/courses/featured` - Get featured courses
- `GET /api/courses/popular` - Get popular courses
- `GET /api/courses/categories` - Get all categories

### User Management
- `GET /api/user/enrollments` - Get user enrollments
- `GET /api/user/progress/:courseId` - Get course progress
- `PUT /api/user/progress/:courseId/:lessonId` - Update lesson progress

### Lessons
- `GET /api/lessons/course/:courseId` - Get course lessons
- `GET /api/lessons/:id` - Get lesson details
- `POST /api/lessons/:id/access` - Mark lesson as accessed
- `GET /api/lessons/:id/next` - Get next lesson

### Payments
- `POST /api/payment/create-order` - Create payment order
- `POST /api/payment/verify` - Verify payment
- `GET /api/payment/status/:orderId` - Get payment status
- `GET /api/payment/history` - Get payment history

## Troubleshooting

### Backend Issues

1. **Server won't start**: Check if PostgreSQL is running and database credentials are correct
2. **Database errors**: Run migrations with `npm run migrate`
3. **Firebase errors**: Verify Firebase Admin SDK configuration
4. **CORS errors**: Check CORS_ORIGIN in .env file

### Flutter Issues

1. **Network errors**: 
   - Check if backend server is running
   - For Android emulator, use `********` instead of `localhost`
   - Check firewall settings

2. **Authentication errors**: Verify Firebase configuration in Flutter app

3. **Build errors**: Run `flutter clean` and `flutter pub get`

### Common Integration Issues

1. **Data not loading**: Check browser/app console for network errors
2. **Authentication not working**: Verify Firebase token is being sent in API requests
3. **CORS errors**: Make sure CORS is properly configured in backend

## Development Notes

- The Flutter app includes fallback to mock data if API calls fail
- All API calls include error handling with graceful degradation
- The backend includes comprehensive logging for debugging
- Rate limiting is enabled to prevent abuse

## Next Steps

1. Set up proper logging and monitoring
2. Add more comprehensive error handling
3. Implement caching for better performance
4. Add automated tests
5. Set up CI/CD pipeline
6. Configure production environment
