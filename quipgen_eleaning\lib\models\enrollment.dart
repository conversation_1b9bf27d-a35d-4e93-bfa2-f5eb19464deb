enum EnrollmentStatus {
  pending,
  active,
  completed,
  cancelled,
  expired,
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  refunded,
}

enum PaymentMethod {
  creditCard,
  debitCard,
  paypal,
  googlePay,
  applePay,
  bankTransfer,
}

class Enrollment {
  final String id;
  final String userId;
  final String courseId;
  final EnrollmentStatus status;
  final DateTime enrolledAt;
  final DateTime? completedAt;
  final DateTime? expiresAt;
  final double amountPaid;
  final String currency;
  final PaymentStatus paymentStatus;
  final String? certificateUrl;
  final Map<String, dynamic> metadata;

  Enrollment({
    required this.id,
    required this.userId,
    required this.courseId,
    required this.status,
    required this.enrolledAt,
    this.completedAt,
    this.expiresAt,
    required this.amountPaid,
    this.currency = 'USD',
    required this.paymentStatus,
    this.certificateUrl,
    this.metadata = const {},
  });

  factory Enrollment.fromJson(Map<String, dynamic> json) {
    return Enrollment(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      courseId: json['courseId'] ?? '',
      status: EnrollmentStatus.values.firstWhere(
        (e) => e.toString() == 'EnrollmentStatus.${json['status']}',
        orElse: () => EnrollmentStatus.pending,
      ),
      enrolledAt: DateTime.parse(json['enrolledAt'] ?? DateTime.now().toIso8601String()),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      amountPaid: (json['amountPaid'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'USD',
      paymentStatus: PaymentStatus.values.firstWhere(
        (e) => e.toString() == 'PaymentStatus.${json['paymentStatus']}',
        orElse: () => PaymentStatus.pending,
      ),
      certificateUrl: json['certificateUrl'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'courseId': courseId,
      'status': status.toString().split('.').last,
      'enrolledAt': enrolledAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'amountPaid': amountPaid,
      'currency': currency,
      'paymentStatus': paymentStatus.toString().split('.').last,
      'certificateUrl': certificateUrl,
      'metadata': metadata,
    };
  }

  bool get isActive => status == EnrollmentStatus.active;
  bool get isCompleted => status == EnrollmentStatus.completed;
  bool get hasCertificate => certificateUrl != null && certificateUrl!.isNotEmpty;
}

class PaymentInfo {
  final String id;
  final String enrollmentId;
  final double amount;
  final String currency;
  final PaymentMethod method;
  final PaymentStatus status;
  final String? transactionId;
  final String? gatewayResponse;
  final DateTime createdAt;
  final DateTime? processedAt;
  final Map<String, dynamic> metadata;

  PaymentInfo({
    required this.id,
    required this.enrollmentId,
    required this.amount,
    this.currency = 'USD',
    required this.method,
    required this.status,
    this.transactionId,
    this.gatewayResponse,
    required this.createdAt,
    this.processedAt,
    this.metadata = const {},
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      id: json['id'] ?? '',
      enrollmentId: json['enrollmentId'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'USD',
      method: PaymentMethod.values.firstWhere(
        (e) => e.toString() == 'PaymentMethod.${json['method']}',
        orElse: () => PaymentMethod.creditCard,
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == 'PaymentStatus.${json['status']}',
        orElse: () => PaymentStatus.pending,
      ),
      transactionId: json['transactionId'],
      gatewayResponse: json['gatewayResponse'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      processedAt: json['processedAt'] != null ? DateTime.parse(json['processedAt']) : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enrollmentId': enrollmentId,
      'amount': amount,
      'currency': currency,
      'method': method.toString().split('.').last,
      'status': status.toString().split('.').last,
      'transactionId': transactionId,
      'gatewayResponse': gatewayResponse,
      'createdAt': createdAt.toIso8601String(),
      'processedAt': processedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  String get formattedAmount => '${currency == 'USD' ? '\$' : '₹'}${amount.toStringAsFixed(2)}';
  String get methodDisplayName {
    switch (method) {
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.googlePay:
        return 'Google Pay';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
    }
  }
}

class Coupon {
  final String id;
  final String code;
  final String description;
  final double discountAmount;
  final double discountPercentage;
  final double minimumAmount;
  final DateTime validFrom;
  final DateTime validUntil;
  final int usageLimit;
  final int usedCount;
  final bool isActive;
  final List<String> applicableCourseIds;

  Coupon({
    required this.id,
    required this.code,
    required this.description,
    this.discountAmount = 0,
    this.discountPercentage = 0,
    this.minimumAmount = 0,
    required this.validFrom,
    required this.validUntil,
    this.usageLimit = 1,
    this.usedCount = 0,
    this.isActive = true,
    this.applicableCourseIds = const [],
  });

  factory Coupon.fromJson(Map<String, dynamic> json) {
    return Coupon(
      id: json['id'] ?? '',
      code: json['code'] ?? '',
      description: json['description'] ?? '',
      discountAmount: (json['discountAmount'] ?? 0).toDouble(),
      discountPercentage: (json['discountPercentage'] ?? 0).toDouble(),
      minimumAmount: (json['minimumAmount'] ?? 0).toDouble(),
      validFrom: DateTime.parse(json['validFrom'] ?? DateTime.now().toIso8601String()),
      validUntil: DateTime.parse(json['validUntil'] ?? DateTime.now().toIso8601String()),
      usageLimit: json['usageLimit'] ?? 1,
      usedCount: json['usedCount'] ?? 0,
      isActive: json['isActive'] ?? true,
      applicableCourseIds: List<String>.from(json['applicableCourseIds'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'description': description,
      'discountAmount': discountAmount,
      'discountPercentage': discountPercentage,
      'minimumAmount': minimumAmount,
      'validFrom': validFrom.toIso8601String(),
      'validUntil': validUntil.toIso8601String(),
      'usageLimit': usageLimit,
      'usedCount': usedCount,
      'isActive': isActive,
      'applicableCourseIds': applicableCourseIds,
    };
  }

  bool get isValid {
    final now = DateTime.now();
    return isActive && 
           now.isAfter(validFrom) && 
           now.isBefore(validUntil) && 
           usedCount < usageLimit;
  }

  double calculateDiscount(double amount) {
    if (!isValid || amount < minimumAmount) return 0;
    
    if (discountPercentage > 0) {
      return amount * (discountPercentage / 100);
    } else {
      return discountAmount;
    }
  }

  bool isApplicableForCourse(String courseId) {
    return applicableCourseIds.isEmpty || applicableCourseIds.contains(courseId);
  }
}
