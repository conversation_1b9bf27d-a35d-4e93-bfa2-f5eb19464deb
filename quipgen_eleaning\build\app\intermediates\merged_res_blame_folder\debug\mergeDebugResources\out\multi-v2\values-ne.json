{"logs": [{"outputFile": "com.quipgen.elearning.app-mergeDebugResources-47:/values-ne/values-ne.xml", "map": [{"source": "D:\\Gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2888,2991,3094,3196,3302,3400,3500,6854", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "2986,3089,3191,3297,3395,3495,3603,6950"}}, {"source": "D:\\Gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,188,272,353,500,669,767", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "183,267,348,495,664,762,842"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5939,6129,6547,6628,6955,7124,7222", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "6017,6208,6623,6770,7119,7217,7297"}}, {"source": "D:\\Gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4654", "endColumns": "163", "endOffsets": "4813"}}, {"source": "D:\\Gradle\\caches\\8.12\\transforms\\a75f01a7707dc356bd2b3ef3fe83d0a8\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,304,458,588,701,868,1000,1106,1207,1383,1493,1653,1782,1926,2074,2136,2204", "endColumns": "110,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "303,457,587,700,867,999,1105,1206,1382,1492,1652,1781,1925,2073,2135,2203,2291"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3608,3723,3881,4015,4132,4303,4439,4549,4818,4998,5112,5276,5409,5557,5709,5775,5847", "endColumns": "114,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "3718,3876,4010,4127,4298,4434,4544,4649,4993,5107,5271,5404,5552,5704,5770,5842,5934"}}, {"source": "D:\\Gradle\\caches\\8.12\\transforms\\f660676bf4ab7e115492941cf8444d98\\transformed\\browser-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,274,388", "endColumns": "106,111,113,107", "endOffsets": "157,269,383,491"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "6022,6213,6325,6439", "endColumns": "106,111,113,107", "endOffsets": "6124,6320,6434,6542"}}, {"source": "D:\\Gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1946,2059,2160,2256,2369,2479,2603,2777,2888", "endColumns": "110,110,107,90,106,126,83,79,90,91,94,93,100,92,94,93,90,90,84,112,100,95,112,109,123,173,110,78", "endOffsets": "211,322,430,521,628,755,839,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1941,2054,2155,2251,2364,2474,2598,2772,2883,2962"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1946,2059,2160,2256,2369,2479,2603,2777,6775", "endColumns": "110,110,107,90,106,126,83,79,90,91,94,93,100,92,94,93,90,90,84,112,100,95,112,109,123,173,110,78", "endOffsets": "211,322,430,521,628,755,839,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1941,2054,2155,2251,2364,2474,2598,2772,2883,6849"}}]}]}