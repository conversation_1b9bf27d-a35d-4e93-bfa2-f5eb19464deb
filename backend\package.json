{"name": "quipgen-elearning-backend", "version": "1.0.0", "description": "Backend API for QuipGen E-Learning Platform with Razorpay integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["nodejs", "express", "postgresql", "firebase", "razorpay", "elearning"], "author": "QuipGen Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "pg-pool": "^3.6.1", "firebase-admin": "^12.0.0", "razorpay": "^2.9.2", "crypto": "^1.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0"}}